'use client';

/**
 * <PERSON><PERSON><PERSON> hằng số và cấu hình cho module đơn hàng
 */

// Tên bảng trong Supabase
export const TABLE_NAME = 'orders';
export const ORDER_ITEMS_TABLE = 'order_items';
export const ORDER_STATUSES_TABLE = 'order_statuses';
export const ORDER_HISTORY_TABLE = 'order_history';
export const CUSTOMERS_TABLE = 'customers';
export const CUSTOMER_ADDRESSES_TABLE = 'customer_addresses';

// Cấu hình mặc định
export const DEFAULT_ORDER_OPTIONS = {
  orderBy: 'createdAt',
  ascending: false,
};

// Trạng thái đơn hàng - Tối ưu cho doanh nghiệp
export const ORDER_STATUS = {
  // Quy trình chính
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PAID: 'paid',
  PROCESSING: 'processing',
  SHIPPING: 'shipping',
  DELIVERED: 'delivered',
  COMPLETED: 'completed',

  // Trạng thái đặc biệt
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
};

// Phương thức thanh toán
export const PAYMENT_METHODS = {
  COD: 'cod',
  BANK_TRANSFER: 'bank_transfer',
  CREDIT_CARD: 'credit_card',
  MOMO: 'momo',
  ZALOPAY: 'zalopay',
};

// Phương thức vận chuyển
export const SHIPPING_METHODS = {
  STANDARD: 'standard',
  EXPRESS: 'express',
  FREE: 'free',
};

// Tên trường trong database - đồng bộ chính xác với schema DB
export const DB_FIELDS = {
  ID: 'id',
  STORE_ID: 'storeId',
  CUSTOMER_ID: 'customerId',
  STATUS: 'status',
  ORDER_NUMBER: 'orderNumber',
  TOTAL_AMOUNT: 'totalAmount',
  SUBTOTAL: 'subtotal',
  SHIPPING_AMOUNT: 'shippingAmount',
  TAX_AMOUNT: 'taxAmount',
  DISCOUNT_AMOUNT: 'discountAmount',
  SHIPPING_ADDRESS_ID: 'shippingAddressId',
  BILLING_ADDRESS_ID: 'billingAddressId',
  SHIPPING_METHOD: 'shippingMethod',
  PAYMENT_METHOD: 'paymentMethod',
  NOTES: 'notes',
  CUSTOMER_EMAIL: 'customerEmail',
  CUSTOMER_PHONE: 'customerPhone',
  CUSTOMER_NAME: 'customerName',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
};

// Tên trường trong UI form
export const FORM_FIELDS = {
  ORDER_NUMBER: 'orderNumber',
  CUSTOMER_ID: 'customerId',
  STATUS: 'status',
  TOTAL_AMOUNT: 'totalAmount',
  SUBTOTAL: 'subtotal',
  SHIPPING_AMOUNT: 'shippingAmount',
  TAX_AMOUNT: 'taxAmount',
  DISCOUNT_AMOUNT: 'discountAmount',
  SHIPPING_ADDRESS_ID: 'shippingAddressId',
  BILLING_ADDRESS_ID: 'billingAddressId',
  SHIPPING_METHOD: 'shippingMethod',
  PAYMENT_METHOD: 'paymentMethod',
  NOTES: 'notes',
  CUSTOMER_EMAIL: 'customerEmail',
  CUSTOMER_PHONE: 'customerPhone',
  CUSTOMER_NAME: 'customerName',
  ORDER_ITEMS: 'orderItems',
};

// Mapping giữa trường form và trường database
export const FORM_TO_DB_MAPPING = {
  [FORM_FIELDS.ORDER_NUMBER]: DB_FIELDS.ORDER_NUMBER,
  [FORM_FIELDS.CUSTOMER_ID]: DB_FIELDS.CUSTOMER_ID,
  [FORM_FIELDS.STATUS]: DB_FIELDS.STATUS,
  [FORM_FIELDS.TOTAL_AMOUNT]: DB_FIELDS.TOTAL_AMOUNT,
  [FORM_FIELDS.SUBTOTAL]: DB_FIELDS.SUBTOTAL,
  [FORM_FIELDS.SHIPPING_AMOUNT]: DB_FIELDS.SHIPPING_AMOUNT,
  [FORM_FIELDS.TAX_AMOUNT]: DB_FIELDS.TAX_AMOUNT,
  [FORM_FIELDS.DISCOUNT_AMOUNT]: DB_FIELDS.DISCOUNT_AMOUNT,
  [FORM_FIELDS.SHIPPING_ADDRESS_ID]: DB_FIELDS.SHIPPING_ADDRESS_ID,
  [FORM_FIELDS.BILLING_ADDRESS_ID]: DB_FIELDS.BILLING_ADDRESS_ID,
  [FORM_FIELDS.SHIPPING_METHOD]: DB_FIELDS.SHIPPING_METHOD,
  [FORM_FIELDS.PAYMENT_METHOD]: DB_FIELDS.PAYMENT_METHOD,
  [FORM_FIELDS.NOTES]: DB_FIELDS.NOTES,
  [FORM_FIELDS.CUSTOMER_EMAIL]: DB_FIELDS.CUSTOMER_EMAIL,
  [FORM_FIELDS.CUSTOMER_PHONE]: DB_FIELDS.CUSTOMER_PHONE,
  [FORM_FIELDS.CUSTOMER_NAME]: DB_FIELDS.CUSTOMER_NAME,
};

/**
 * Các tùy chọn trạng thái đơn hàng - Đầy đủ đồng bộ với database
 *
 * Đồng bộ hoàn toàn với:
 * - Database enum order_status_enum (16 trạng thái)
 * - Bảng order_statuses với product_type support
 * - Business flows cho từng loại sản phẩm
 */
export const ORDER_STATUS_OPTIONS = [
  // === QUY TRÌNH CHUNG - Áp dụng cho tất cả loại sản phẩm ===
  { value: 'pending', label: 'Chờ xác nhận', color: 'warning', productTypes: ['all'], sortOrder: 10 },
  { value: 'confirmed', label: 'Đã xác nhận', color: 'info', productTypes: ['all'], sortOrder: 20 },
  { value: 'processing', label: 'Đang xử lý', color: 'info', productTypes: ['all'], sortOrder: 30 },
  { value: 'paid', label: 'Đã thanh toán', color: 'success', productTypes: ['all'], sortOrder: 40 },

  // === SẢN PHẨM VẬT LÝ (Simple & Variable) ===
  { value: 'packaging', label: 'Đang đóng gói', color: 'info', productTypes: ['simple', 'variable'], sortOrder: 50 },
  { value: 'shipping', label: 'Đang vận chuyển', color: 'primary', productTypes: ['simple', 'variable'], sortOrder: 60 },
  { value: 'delivered', label: 'Đã giao hàng', color: 'success', productTypes: ['simple', 'variable'], sortOrder: 70 },

  // === SẢN PHẨM SỐ (Digital) ===
  { value: 'preparing', label: 'Đang chuẩn bị', color: 'info', productTypes: ['digital'], sortOrder: 50 },
  { value: 'ready_download', label: 'Sẵn sàng tải xuống', color: 'success', productTypes: ['digital'], sortOrder: 60 },
  { value: 'sent', label: 'Đã gửi', color: 'success', productTypes: ['digital'], sortOrder: 70 },

  // === DỊCH VỤ (Service) ===
  { value: 'scheduling', label: 'Đang lên lịch', color: 'warning', productTypes: ['service'], sortOrder: 50 },
  { value: 'in_progress', label: 'Đang thực hiện', color: 'primary', productTypes: ['service'], sortOrder: 60 },
  { value: 'provided', label: 'Đã cung cấp', color: 'success', productTypes: ['service'], sortOrder: 70 },

  // === TRẠNG THÁI CUỐI ===
  { value: 'completed', label: 'Hoàn thành', color: 'success', productTypes: ['all'], sortOrder: 100 },

  // === TRẠNG THÁI ĐẶC BIỆT ===
  { value: 'cancelled', label: 'Đã hủy', color: 'error', productTypes: ['all'], sortOrder: 110 },
  { value: 'refunded', label: 'Hoàn tiền', color: 'secondary', productTypes: ['all'], sortOrder: 120 },
];

// Các tùy chọn phương thức thanh toán
export const PAYMENT_METHOD_OPTIONS = [
  { value: 'cod', label: 'Thanh toán khi nhận hàng (COD)' },
  { value: 'bank_transfer', label: 'Chuyển khoản ngân hàng' },
  { value: 'credit_card', label: 'Thẻ tín dụng/ghi nợ' },
  { value: 'momo', label: 'Ví MoMo' },
  { value: 'zalopay', label: 'ZaloPay' },
];

// Các tùy chọn phương thức vận chuyển
export const SHIPPING_METHOD_OPTIONS = [
  { value: 'standard', label: 'Vận chuyển tiêu chuẩn' },
  { value: 'express', label: 'Vận chuyển nhanh' },
  { value: 'free', label: 'Miễn phí vận chuyển' },
];

// =====================================================
// UTILITY FUNCTIONS CHO ORDER STATUS
// =====================================================

/**
 * Lấy danh sách trạng thái theo loại sản phẩm
 * @param {string} productType - Loại sản phẩm (simple, variable, digital, service, all)
 * @returns {Array} - Danh sách trạng thái phù hợp
 */
export function getOrderStatusesByProductType(productType = 'all') {
  return ORDER_STATUS_OPTIONS.filter(status =>
    status.productTypes.includes(productType) || status.productTypes.includes('all')
  ).sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
}

/**
 * Lấy thông tin trạng thái theo value
 * @param {string} statusValue - Giá trị trạng thái
 * @returns {Object|null} - Thông tin trạng thái hoặc null
 */
export function getOrderStatusInfo(statusValue) {
  return ORDER_STATUS_OPTIONS.find(status => status.value === statusValue) || null;
}

/**
 * Kiểm tra trạng thái có hợp lệ cho loại sản phẩm không
 * @param {string} statusValue - Giá trị trạng thái
 * @param {string} productType - Loại sản phẩm
 * @returns {boolean} - True nếu hợp lệ
 */
export function isValidStatusForProductType(statusValue, productType) {
  const statusInfo = getOrderStatusInfo(statusValue);
  if (!statusInfo) return false;

  return statusInfo.productTypes.includes(productType) || statusInfo.productTypes.includes('all');
}

/**
 * Lấy màu sắc của trạng thái
 * @param {string} statusValue - Giá trị trạng thái
 * @returns {string} - Màu sắc MUI
 */
export function getOrderStatusColor(statusValue) {
  const statusInfo = getOrderStatusInfo(statusValue);
  return statusInfo?.color || 'default';
}

/**
 * Lấy nhãn hiển thị của trạng thái
 * @param {string} statusValue - Giá trị trạng thái
 * @returns {string} - Nhãn hiển thị
 */
export function getOrderStatusLabel(statusValue) {
  const statusInfo = getOrderStatusInfo(statusValue);
  return statusInfo?.label || statusValue;
}
